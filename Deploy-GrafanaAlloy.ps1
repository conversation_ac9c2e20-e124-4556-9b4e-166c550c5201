#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Grafana Alloy Deployment Wrapper Script
    
.DESCRIPTION
    This PowerShell script provides a secure wrapper for deploying Grafana Alloy
    in security-hardened environments. It validates the installer, manages the
    configuration file, and provides logging for audit purposes.
    
.PARAMETER InstallerPath
    Path to the Grafana Alloy installer executable
    
.PARAMETER ConfigPath
    Path to the Alloy configuration file
    
.PARAMETER InstallPath
    Custom installation path (optional)
    
.PARAMETER ValidateOnly
    Only validate files without installing
    
.PARAMETER LogPath
    Path for deployment logs
    
.EXAMPLE
    .\Deploy-GrafanaAlloy.ps1 -InstallerPath ".\alloy-installer-windows-amd64.exe" -ConfigPath ".\config.alloy"
    
.NOTES
    This script should be signed with a trusted certificate before deployment
    in security-hardened environments.
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateScript({Test-Path $_ -PathType Leaf})]
    [string]$InstallerPath,
    
    [Parameter(Mandatory = $true)]
    [ValidateScript({Test-Path $_ -PathType Leaf})]
    [string]$ConfigPath,
    
    [Parameter(Mandatory = $false)]
    [string]$InstallPath = "",
    
    [Parameter(Mandatory = $false)]
    [switch]$ValidateOnly,
    
    [Parameter(Mandatory = $false)]
    [string]$LogPath = "$env:TEMP\GrafanaAlloyDeployment.log"
)

# Configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Expected file hashes (update these with actual values)
$ExpectedHashes = @{
    Installer = "REPLACE_WITH_ACTUAL_INSTALLER_HASH"
    Config = "REPLACE_WITH_ACTUAL_CONFIG_HASH"
}

# Logging function
function Write-DeploymentLog {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARNING", "ERROR", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    Write-Host $logEntry -ForegroundColor $(
        switch ($Level) {
            "INFO" { "White" }
            "WARNING" { "Yellow" }
            "ERROR" { "Red" }
            "SUCCESS" { "Green" }
        }
    )
    
    Add-Content -Path $LogPath -Value $logEntry -Encoding UTF8
}

# File validation function
function Test-FileIntegrity {
    param(
        [string]$FilePath,
        [string]$ExpectedHash,
        [string]$FileType
    )
    
    Write-DeploymentLog "Validating $FileType file: $FilePath"
    
    if (-not (Test-Path $FilePath)) {
        throw "$FileType file not found: $FilePath"
    }
    
    $actualHash = Get-FileHash -Path $FilePath -Algorithm SHA256
    Write-DeploymentLog "File hash: $($actualHash.Hash)"
    
    if ($ExpectedHash -ne "REPLACE_WITH_ACTUAL_INSTALLER_HASH" -and $ExpectedHash -ne "REPLACE_WITH_ACTUAL_CONFIG_HASH") {
        if ($actualHash.Hash -ne $ExpectedHash) {
            throw "$FileType file integrity check failed. Expected: $ExpectedHash, Actual: $($actualHash.Hash)"
        }
        Write-DeploymentLog "$FileType file integrity verified" "SUCCESS"
    } else {
        Write-DeploymentLog "$FileType file hash validation skipped (placeholder hash)" "WARNING"
    }
    
    return $actualHash.Hash
}

# Service management functions
function Stop-AlloyService {
    $service = Get-Service -Name "Grafana Alloy" -ErrorAction SilentlyContinue
    if ($service -and $service.Status -eq "Running") {
        Write-DeploymentLog "Stopping existing Grafana Alloy service"
        Stop-Service -Name "Grafana Alloy" -Force
        Start-Sleep -Seconds 5
    }
}

function Start-AlloyService {
    $service = Get-Service -Name "Grafana Alloy" -ErrorAction SilentlyContinue
    if ($service) {
        Write-DeploymentLog "Starting Grafana Alloy service"
        Start-Service -Name "Grafana Alloy"
        
        # Wait for service to start
        $timeout = 30
        $elapsed = 0
        while ($elapsed -lt $timeout) {
            $service = Get-Service -Name "Grafana Alloy" -ErrorAction SilentlyContinue
            if ($service.Status -eq "Running") {
                Write-DeploymentLog "Grafana Alloy service started successfully" "SUCCESS"
                return
            }
            Start-Sleep -Seconds 2
            $elapsed += 2
        }
        Write-DeploymentLog "Service start timeout reached" "WARNING"
    }
}

# Main deployment function
function Deploy-GrafanaAlloy {
    try {
        Write-DeploymentLog "Starting Grafana Alloy deployment" "INFO"
        Write-DeploymentLog "Installer: $InstallerPath"
        Write-DeploymentLog "Config: $ConfigPath"
        Write-DeploymentLog "Log: $LogPath"
        
        # Validate files
        $installerHash = Test-FileIntegrity -FilePath $InstallerPath -ExpectedHash $ExpectedHashes.Installer -FileType "Installer"
        $configHash = Test-FileIntegrity -FilePath $ConfigPath -ExpectedHash $ExpectedHashes.Config -FileType "Config"
        
        if ($ValidateOnly) {
            Write-DeploymentLog "Validation completed successfully. Exiting (ValidateOnly mode)." "SUCCESS"
            return
        }
        
        # Stop existing service
        Stop-AlloyService
        
        # Run installer
        Write-DeploymentLog "Running Grafana Alloy installer"
        $installerArgs = @("/S")  # Silent install for NSIS
        if ($InstallPath) {
            $installerArgs += "/D=$InstallPath"
        }
        
        $process = Start-Process -FilePath $InstallerPath -ArgumentList $installerArgs -Wait -PassThru
        
        if ($process.ExitCode -ne 0) {
            throw "Installer failed with exit code: $($process.ExitCode)"
        }
        
        Write-DeploymentLog "Installer completed successfully" "SUCCESS"
        
        # Deploy configuration file
        $defaultConfigPath = "${env:ProgramFiles}\GrafanaLabs\Alloy\config.alloy"
        $configDestination = if (Test-Path $defaultConfigPath) { $defaultConfigPath } else { "${env:ProgramData}\GrafanaLabs\Alloy\config.alloy" }
        
        Write-DeploymentLog "Deploying configuration to: $configDestination"
        
        # Create directory if it doesn't exist
        $configDir = Split-Path $configDestination -Parent
        if (-not (Test-Path $configDir)) {
            New-Item -Path $configDir -ItemType Directory -Force | Out-Null
        }
        
        # Backup existing config if present
        if (Test-Path $configDestination) {
            $backupPath = "$configDestination.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            Copy-Item -Path $configDestination -Destination $backupPath
            Write-DeploymentLog "Existing config backed up to: $backupPath"
        }
        
        # Copy new configuration
        Copy-Item -Path $ConfigPath -Destination $configDestination -Force
        Write-DeploymentLog "Configuration deployed successfully" "SUCCESS"
        
        # Start service
        Start-AlloyService
        
        Write-DeploymentLog "Grafana Alloy deployment completed successfully" "SUCCESS"
        
    } catch {
        Write-DeploymentLog "Deployment failed: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# Script execution
try {
    # Create log directory if needed
    $logDir = Split-Path $LogPath -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -Path $logDir -ItemType Directory -Force | Out-Null
    }
    
    Deploy-GrafanaAlloy
    
} catch {
    Write-DeploymentLog "Script execution failed: $($_.Exception.Message)" "ERROR"
    exit 1
}
