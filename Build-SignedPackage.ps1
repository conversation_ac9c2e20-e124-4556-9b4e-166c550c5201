#Requires -Version 5.1

<#
.SYNOPSIS
    Build and sign the Grafana Alloy deployment package
    
.DESCRIPTION
    This script automates the process of building and signing the MSI package
    for Grafana Alloy deployment in security-hardened environments.
    
.PARAMETER CertificatePath
    Path to the code signing certificate (.pfx file)
    
.PARAMETER CertificatePassword
    Password for the certificate (will prompt if not provided)
    
.PARAMETER TimestampServer
    Timestamp server URL for code signing
    
.PARAMETER WixPath
    Path to WiX toolset installation
    
.PARAMETER OutputPath
    Output directory for the signed package
    
.EXAMPLE
    .\Build-SignedPackage.ps1 -CertificatePath "cert.pfx" -WixPath "C:\Program Files (x86)\WiX Toolset v3.11\bin"
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateScript({Test-Path $_ -PathType Leaf})]
    [string]$CertificatePath,
    
    [Parameter(Mandatory = $false)]
    [SecureString]$CertificatePassword,
    
    [Parameter(Mandatory = $false)]
    [string]$TimestampServer = "http://timestamp.digicert.com",
    
    [Parameter(Mandatory = $false)]
    [string]$WixPath = "${env:ProgramFiles(x86)}\WiX Toolset v3.11\bin",
    
    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".\dist"
)

$ErrorActionPreference = "Stop"

# Functions
function Write-BuildLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch ($Level) {
            "INFO" { "White" }
            "SUCCESS" { "Green" }
            "WARNING" { "Yellow" }
            "ERROR" { "Red" }
        }
    )
}

function Test-Prerequisites {
    Write-BuildLog "Checking prerequisites..."
    
    # Check WiX installation
    $candlePath = Join-Path $WixPath "candle.exe"
    $lightPath = Join-Path $WixPath "light.exe"
    
    if (-not (Test-Path $candlePath)) {
        throw "WiX candle.exe not found at: $candlePath"
    }
    
    if (-not (Test-Path $lightPath)) {
        throw "WiX light.exe not found at: $lightPath"
    }
    
    # Check signtool
    $signtoolPath = Get-Command "signtool.exe" -ErrorAction SilentlyContinue
    if (-not $signtoolPath) {
        throw "signtool.exe not found. Please install Windows SDK."
    }
    
    # Check required files
    $requiredFiles = @(
        "GrafanaAlloyWrapper.wxs",
        "Deploy-GrafanaAlloy.ps1",
        "Deploy-Alloy.bat",
        "alloy-installer-windows-amd64.exe",
        "config.alloy"
    )
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            throw "Required file not found: $file"
        }
    }
    
    Write-BuildLog "Prerequisites check passed" "SUCCESS"
}

function Update-FileHashes {
    Write-BuildLog "Updating file hashes in PowerShell script..."
    
    # Calculate hashes
    $installerHash = (Get-FileHash -Path "alloy-installer-windows-amd64.exe" -Algorithm SHA256).Hash
    $configHash = (Get-FileHash -Path "config.alloy" -Algorithm SHA256).Hash
    
    Write-BuildLog "Installer hash: $installerHash"
    Write-BuildLog "Config hash: $configHash"
    
    # Update PowerShell script
    $scriptContent = Get-Content "Deploy-GrafanaAlloy.ps1" -Raw
    $scriptContent = $scriptContent -replace 'Installer = "REPLACE_WITH_ACTUAL_INSTALLER_HASH"', "Installer = `"$installerHash`""
    $scriptContent = $scriptContent -replace 'Config = "REPLACE_WITH_ACTUAL_CONFIG_HASH"', "Config = `"$configHash`""
    
    Set-Content "Deploy-GrafanaAlloy.ps1" -Value $scriptContent -Encoding UTF8
    
    Write-BuildLog "File hashes updated successfully" "SUCCESS"
}

function Sign-PowerShellScript {
    Write-BuildLog "Signing PowerShell script..."
    
    # Load certificate
    if (-not $CertificatePassword) {
        $CertificatePassword = Read-Host "Enter certificate password" -AsSecureString
    }
    
    $cert = Get-PfxCertificate -FilePath $CertificatePath -Password $CertificatePassword
    
    # Sign the script
    $signature = Set-AuthenticodeSignature -FilePath "Deploy-GrafanaAlloy.ps1" -Certificate $cert -TimestampServer $TimestampServer
    
    if ($signature.Status -ne "Valid") {
        throw "Failed to sign PowerShell script: $($signature.StatusMessage)"
    }
    
    Write-BuildLog "PowerShell script signed successfully" "SUCCESS"
}

function Build-MSI {
    Write-BuildLog "Building MSI package..."
    
    $candlePath = Join-Path $WixPath "candle.exe"
    $lightPath = Join-Path $WixPath "light.exe"
    
    # Compile WiX source
    Write-BuildLog "Compiling WiX source..."
    $candleArgs = @("GrafanaAlloyWrapper.wxs", "-out", "GrafanaAlloyWrapper.wixobj")
    $candleProcess = Start-Process -FilePath $candlePath -ArgumentList $candleArgs -Wait -PassThru -NoNewWindow
    
    if ($candleProcess.ExitCode -ne 0) {
        throw "WiX compilation failed with exit code: $($candleProcess.ExitCode)"
    }
    
    # Link to create MSI
    Write-BuildLog "Linking MSI package..."
    $lightArgs = @("-ext", "WixUIExtension", "GrafanaAlloyWrapper.wixobj", "-out", "GrafanaAlloyWrapper.msi")
    $lightProcess = Start-Process -FilePath $lightPath -ArgumentList $lightArgs -Wait -PassThru -NoNewWindow
    
    if ($lightProcess.ExitCode -ne 0) {
        throw "WiX linking failed with exit code: $($lightProcess.ExitCode)"
    }
    
    Write-BuildLog "MSI package built successfully" "SUCCESS"
}

function Sign-MSI {
    Write-BuildLog "Signing MSI package..."
    
    # Convert SecureString to plain text for signtool
    $plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($CertificatePassword))
    
    $signtoolArgs = @(
        "sign",
        "/f", $CertificatePath,
        "/p", $plainPassword,
        "/t", $TimestampServer,
        "/d", "Grafana Alloy Deployment Wrapper",
        "GrafanaAlloyWrapper.msi"
    )
    
    $signtoolProcess = Start-Process -FilePath "signtool.exe" -ArgumentList $signtoolArgs -Wait -PassThru -NoNewWindow
    
    if ($signtoolProcess.ExitCode -ne 0) {
        throw "MSI signing failed with exit code: $($signtoolProcess.ExitCode)"
    }
    
    # Verify signature
    Write-BuildLog "Verifying MSI signature..."
    $verifyProcess = Start-Process -FilePath "signtool.exe" -ArgumentList @("verify", "/pa", "GrafanaAlloyWrapper.msi") -Wait -PassThru -NoNewWindow
    
    if ($verifyProcess.ExitCode -ne 0) {
        throw "MSI signature verification failed"
    }
    
    Write-BuildLog "MSI package signed and verified successfully" "SUCCESS"
}

function Create-DistributionPackage {
    Write-BuildLog "Creating distribution package..."
    
    # Create output directory
    if (-not (Test-Path $OutputPath)) {
        New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
    }
    
    # Copy files to distribution
    $filesToCopy = @(
        "GrafanaAlloyWrapper.msi",
        "Deploy-GrafanaAlloy.ps1",
        "Deploy-Alloy.bat",
        "README-Deployment.md",
        "alloy-installer-windows-amd64.exe",
        "config.alloy"
    )
    
    foreach ($file in $filesToCopy) {
        Copy-Item -Path $file -Destination $OutputPath -Force
        Write-BuildLog "Copied: $file"
    }
    
    # Create deployment instructions
    $instructions = @"
Grafana Alloy Signed Deployment Package
======================================

This package contains a signed MSI installer for deploying Grafana Alloy
in security-hardened environments.

Quick Start:
1. Install the MSI package: msiexec /i GrafanaAlloyWrapper.msi
2. Run deployment: Deploy-Alloy.bat deploy

For detailed instructions, see README-Deployment.md

Package built on: $(Get-Date)
"@
    
    Set-Content -Path (Join-Path $OutputPath "DEPLOYMENT-INSTRUCTIONS.txt") -Value $instructions
    
    Write-BuildLog "Distribution package created in: $OutputPath" "SUCCESS"
}

# Main execution
try {
    Write-BuildLog "Starting build process for Grafana Alloy signed package"
    
    Test-Prerequisites
    Update-FileHashes
    Sign-PowerShellScript
    Build-MSI
    Sign-MSI
    Create-DistributionPackage
    
    Write-BuildLog "Build process completed successfully!" "SUCCESS"
    Write-BuildLog "Signed package available in: $OutputPath"
    
} catch {
    Write-BuildLog "Build failed: $($_.Exception.Message)" "ERROR"
    exit 1
} finally {
    # Cleanup temporary files
    $tempFiles = @("GrafanaAlloyWrapper.wixobj", "GrafanaAlloyWrapper.wixpdb")
    foreach ($file in $tempFiles) {
        if (Test-Path $file) {
            Remove-Item $file -Force
        }
    }
}
