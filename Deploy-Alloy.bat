@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM Grafana Alloy Deployment Batch Script
REM 
REM This batch script provides a simple interface for deploying Grafana Alloy
REM in security-hardened environments. It calls the PowerShell wrapper script
REM with appropriate parameters.
REM 
REM Usage: Deploy-Alloy.bat [validate|deploy|help]
REM ============================================================================

set "SCRIPT_DIR=%~dp0"
set "PS_SCRIPT=%SCRIPT_DIR%Deploy-GrafanaAlloy.ps1"
set "INSTALLER=%SCRIPT_DIR%alloy-installer-windows-amd64.exe"
set "CONFIG=%SCRIPT_DIR%config.alloy"
set "LOG_FILE=%TEMP%\GrafanaAlloyDeployment.log"

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Parse command line arguments
set "ACTION=%1"
if "%ACTION%"=="" set "ACTION=deploy"

if /i "%ACTION%"=="help" goto :show_help
if /i "%ACTION%"=="validate" goto :validate_files
if /i "%ACTION%"=="deploy" goto :deploy_alloy

echo ERROR: Unknown action '%ACTION%'
goto :show_help

:validate_files
echo ============================================================================
echo Validating Grafana Alloy files...
echo ============================================================================
echo.

REM Check if required files exist
if not exist "%PS_SCRIPT%" (
    echo ERROR: PowerShell script not found: %PS_SCRIPT%
    exit /b 1
)

if not exist "%INSTALLER%" (
    echo ERROR: Installer not found: %INSTALLER%
    exit /b 1
)

if not exist "%CONFIG%" (
    echo ERROR: Configuration file not found: %CONFIG%
    exit /b 1
)

echo Files found:
echo - PowerShell Script: %PS_SCRIPT%
echo - Installer: %INSTALLER%
echo - Configuration: %CONFIG%
echo.

REM Run validation
echo Running file validation...
powershell.exe -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -InstallerPath "%INSTALLER%" -ConfigPath "%CONFIG%" -ValidateOnly -LogPath "%LOG_FILE%"

if %errorLevel% equ 0 (
    echo.
    echo ============================================================================
    echo Validation completed successfully!
    echo ============================================================================
) else (
    echo.
    echo ============================================================================
    echo Validation failed! Check the log file: %LOG_FILE%
    echo ============================================================================
    exit /b %errorLevel%
)

goto :end

:deploy_alloy
echo ============================================================================
echo Deploying Grafana Alloy...
echo ============================================================================
echo.

REM Check if required files exist
if not exist "%PS_SCRIPT%" (
    echo ERROR: PowerShell script not found: %PS_SCRIPT%
    exit /b 1
)

if not exist "%INSTALLER%" (
    echo ERROR: Installer not found: %INSTALLER%
    exit /b 1
)

if not exist "%CONFIG%" (
    echo ERROR: Configuration file not found: %CONFIG%
    exit /b 1
)

echo Files to be deployed:
echo - Installer: %INSTALLER%
echo - Configuration: %CONFIG%
echo - Log file: %LOG_FILE%
echo.

REM Confirm deployment
set /p "CONFIRM=Do you want to proceed with the deployment? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Deployment cancelled by user.
    goto :end
)

echo.
echo Starting deployment...

REM Run deployment
powershell.exe -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -InstallerPath "%INSTALLER%" -ConfigPath "%CONFIG%" -LogPath "%LOG_FILE%"

if %errorLevel% equ 0 (
    echo.
    echo ============================================================================
    echo Deployment completed successfully!
    echo ============================================================================
    echo.
    echo Next steps:
    echo 1. Verify the Grafana Alloy service is running
    echo 2. Check the service logs for any issues
    echo 3. Test connectivity to your Grafana instance
    echo.
    echo Service status:
    sc query "Grafana Alloy" 2>nul
    if %errorLevel% equ 0 (
        echo Grafana Alloy service is installed.
    ) else (
        echo WARNING: Grafana Alloy service not found.
    )
) else (
    echo.
    echo ============================================================================
    echo Deployment failed! Check the log file: %LOG_FILE%
    echo ============================================================================
    exit /b %errorLevel%
)

goto :end

:show_help
echo ============================================================================
echo Grafana Alloy Deployment Script
echo ============================================================================
echo.
echo This script deploys Grafana Alloy in security-hardened environments using
echo a signed wrapper package.
echo.
echo Usage: %~nx0 [action]
echo.
echo Actions:
echo   validate  - Validate installer and configuration files only
echo   deploy    - Deploy Grafana Alloy (default action)
echo   help      - Show this help message
echo.
echo Examples:
echo   %~nx0                    Deploy Grafana Alloy
echo   %~nx0 validate           Validate files only
echo   %~nx0 deploy             Deploy Grafana Alloy
echo   %~nx0 help               Show this help
echo.
echo Requirements:
echo - Must be run as Administrator
echo - PowerShell execution policy must allow script execution
echo - All required files must be present in the script directory
echo.
echo Files required:
echo - Deploy-GrafanaAlloy.ps1 (PowerShell deployment script)
echo - alloy-installer-windows-amd64.exe (Grafana Alloy installer)
echo - config.alloy (Alloy configuration file)
echo.
echo Log file location: %LOG_FILE%
echo ============================================================================

:end
echo.
if exist "%LOG_FILE%" (
    echo Deployment log available at: %LOG_FILE%
)
echo.
pause
