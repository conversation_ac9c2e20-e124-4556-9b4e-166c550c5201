# Grafana Alloy Signed Deployment Wrapper

This package provides a comprehensive solution for deploying Grafana Alloy in security-hardened environments where unsigned executables are not permitted.

## Overview

The solution wraps the unsigned Grafana Alloy installer and configuration file in a signed package that can be deployed in enterprise environments with strict security policies.

## Package Contents

- `Deploy-GrafanaAlloy.ps1` - PowerShell deployment script with validation and logging
- `Deploy-Alloy.bat` - Batch wrapper for easy deployment
- `GrafanaAlloyWrapper.wxs` - WiX source file for creating signed MSI package
- `alloy-installer-windows-amd64.exe` - Original Grafana Alloy installer
- `config.alloy` - Alloy configuration file
- `README-Deployment.md` - This documentation

## Security Features

### File Integrity Validation
- SHA256 hash verification of installer and configuration files
- Prevents tampering and ensures file integrity
- Configurable expected hash values

### Comprehensive Logging
- Detailed deployment logs for audit purposes
- Timestamped entries with severity levels
- Configurable log file location

### Service Management
- Graceful handling of existing Alloy installations
- Service stop/start management
- Configuration backup before updates

## Deployment Options

### Option 1: Direct PowerShell Deployment

```powershell
# Validate files only
.\Deploy-GrafanaAlloy.ps1 -InstallerPath ".\alloy-installer-windows-amd64.exe" -ConfigPath ".\config.alloy" -ValidateOnly

# Deploy Grafana Alloy
.\Deploy-GrafanaAlloy.ps1 -InstallerPath ".\alloy-installer-windows-amd64.exe" -ConfigPath ".\config.alloy"

# Deploy with custom installation path
.\Deploy-GrafanaAlloy.ps1 -InstallerPath ".\alloy-installer-windows-amd64.exe" -ConfigPath ".\config.alloy" -InstallPath "C:\CustomPath\Alloy"
```

### Option 2: Batch Script Deployment

```batch
# Validate files
Deploy-Alloy.bat validate

# Deploy Grafana Alloy
Deploy-Alloy.bat deploy

# Show help
Deploy-Alloy.bat help
```

### Option 3: MSI Package Deployment

```batch
# Install the wrapper package
msiexec /i GrafanaAlloyWrapper.msi /quiet

# Then run deployment from installed location
"C:\Program Files\YourOrganization\GrafanaAlloyWrapper\Deploy-Alloy.bat" deploy
```

## Creating and Signing the MSI Package

### Prerequisites

1. **WiX Toolset**: Download and install from https://wixtoolset.org/
2. **Code Signing Certificate**: Obtain from your organization's PKI or trusted CA
3. **Windows SDK**: For signtool.exe

### Building the MSI

```batch
# Compile the WiX source
candle.exe GrafanaAlloyWrapper.wxs

# Link to create MSI
light.exe -ext WixUIExtension GrafanaAlloyWrapper.wixobj -o GrafanaAlloyWrapper.msi
```

### Signing the Package

#### Sign the PowerShell Script
```powershell
# Sign the PowerShell script
Set-AuthenticodeSignature -FilePath "Deploy-GrafanaAlloy.ps1" -Certificate $cert -TimestampServer "http://timestamp.digicert.com"
```

#### Sign the MSI Package
```batch
# Sign the MSI package
signtool.exe sign /f "YourCertificate.pfx" /p "CertificatePassword" /t "http://timestamp.digicert.com" /d "Grafana Alloy Deployment Wrapper" GrafanaAlloyWrapper.msi

# Verify the signature
signtool.exe verify /pa GrafanaAlloyWrapper.msi
```

## Configuration Management

### Updating File Hashes

Before deployment, update the expected file hashes in `Deploy-GrafanaAlloy.ps1`:

```powershell
# Calculate hashes
$installerHash = (Get-FileHash -Path "alloy-installer-windows-amd64.exe" -Algorithm SHA256).Hash
$configHash = (Get-FileHash -Path "config.alloy" -Algorithm SHA256).Hash

# Update the script with actual values
$ExpectedHashes = @{
    Installer = "$installerHash"
    Config = "$configHash"
}
```

### Customizing Configuration

Edit `config.alloy` to match your environment:

```alloy
remotecfg {
  url            = "https://your-grafana-instance.com"
  id             = "your-agent-id"
  poll_frequency = "60s"
  proxy_url      = "http://your-proxy.com"

  basic_auth {
    username = "your-username"
    password = "your-password"
  }
}
```

## Enterprise Deployment

### Group Policy Deployment

1. Sign the MSI package with your organization's certificate
2. Deploy via Group Policy Software Installation
3. Use transform files (.mst) for environment-specific configurations

### SCCM Deployment

1. Create SCCM application with signed MSI
2. Configure detection methods
3. Set deployment requirements and dependencies

### PowerShell DSC

```powershell
Configuration GrafanaAlloyDeployment {
    Import-DscResource -ModuleName PSDesiredStateConfiguration
    
    Package GrafanaAlloyWrapper {
        Name = "Grafana Alloy Deployment Wrapper"
        Path = "\\server\share\GrafanaAlloyWrapper.msi"
        ProductId = "********-1234-1234-1234-************"
        Ensure = "Present"
    }
}
```

## Troubleshooting

### Common Issues

1. **PowerShell Execution Policy**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine
   ```

2. **Service Start Failures**
   - Check Windows Event Log
   - Verify configuration file syntax
   - Ensure network connectivity

3. **Permission Issues**
   - Run as Administrator
   - Check file system permissions
   - Verify service account permissions

### Log Analysis

Check the deployment log for detailed information:
```batch
type "%TEMP%\GrafanaAlloyDeployment.log"
```

## Security Considerations

1. **Certificate Management**: Ensure signing certificates are properly secured
2. **Hash Validation**: Always update expected hashes when files change
3. **Network Security**: Configure proxy settings appropriately
4. **Service Account**: Use dedicated service account with minimal privileges
5. **File Permissions**: Restrict access to configuration files containing credentials

## Support

For issues with this deployment wrapper:
1. Check the deployment logs
2. Verify all prerequisites are met
3. Ensure proper signing of all components
4. Contact your system administrator for certificate-related issues

For Grafana Alloy specific issues, refer to the official documentation:
https://grafana.com/docs/alloy/
