<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  
  <!-- Product Definition -->
  <Product Id="*" 
           Name="Grafana Alloy Deployment Wrapper" 
           Language="1033" 
           Version="*******" 
           Manufacturer="Your Organization" 
           UpgradeCode="12345678-1234-1234-1234-123456789012">
    
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine" 
             Description="Signed wrapper for Grafana Alloy deployment"
             Comments="Secure deployment package for Grafana Alloy in hardened environments" />
    
    <!-- Media and Cabinet -->
    <MediaTemplate EmbedCab="yes" />
    
    <!-- Major Upgrade Logic -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />
    
    <!-- Properties -->
    <Property Id="ARPPRODUCTICON" Value="AlloyIcon.ico" />
    <Property Id="ARPHELPLINK" Value="https://grafana.com/docs/alloy/" />
    <Property Id="ARPURLINFOABOUT" Value="https://grafana.com/oss/alloy/" />
    <Property Id="ARPNOMODIFY" Value="1" />
    <Property Id="ARPNOREPAIR" Value="1" />
    
    <!-- Custom Properties -->
    <Property Id="INSTALLERPATH" Value="[INSTALLDIR]alloy-installer-windows-amd64.exe" />
    <Property Id="CONFIGPATH" Value="[INSTALLDIR]config.alloy" />
    <Property Id="POWERSHELLPATH" Value="[INSTALLDIR]Deploy-GrafanaAlloy.ps1" />
    
    <!-- Directory Structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="CompanyFolder" Name="YourOrganization">
          <Directory Id="INSTALLDIR" Name="GrafanaAlloyWrapper">
            
            <!-- Embedded Files Component -->
            <Component Id="EmbeddedFiles" Guid="*">
              <File Id="AlloyInstaller" 
                    Source="alloy-installer-windows-amd64.exe" 
                    KeyPath="yes" 
                    Checksum="yes" />
              
              <File Id="AlloyConfig" 
                    Source="config.alloy" 
                    Checksum="yes" />
              
              <File Id="PowerShellScript" 
                    Source="Deploy-GrafanaAlloy.ps1" 
                    Checksum="yes" />
              
              <File Id="DeploymentBatch" 
                    Source="Deploy-Alloy.bat" 
                    Checksum="yes" />
              
              <File Id="ReadmeFile" 
                    Source="README-Deployment.md" 
                    Checksum="yes" />
            </Component>
            
            <!-- Registry Component for Uninstall -->
            <Component Id="RegistryEntries" Guid="*">
              <RegistryKey Root="HKLM" Key="SOFTWARE\YourOrganization\GrafanaAlloyWrapper">
                <RegistryValue Name="InstallPath" Value="[INSTALLDIR]" Type="string" KeyPath="yes" />
                <RegistryValue Name="Version" Value="*******" Type="string" />
                <RegistryValue Name="InstalledDate" Value="[Date]" Type="string" />
              </RegistryKey>
            </Component>
            
          </Directory>
        </Directory>
      </Directory>
      
      <!-- Start Menu Shortcuts -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Grafana Alloy Wrapper">
          <Component Id="ApplicationShortcut" Guid="*">
            <Shortcut Id="ApplicationStartMenuShortcut"
                      Name="Deploy Grafana Alloy"
                      Description="Deploy Grafana Alloy using secure wrapper"
                      Target="[INSTALLDIR]Deploy-Alloy.bat"
                      WorkingDirectory="INSTALLDIR" />
            
            <Shortcut Id="ReadmeShortcut"
                      Name="Deployment Instructions"
                      Description="Read deployment instructions"
                      Target="[INSTALLDIR]README-Deployment.md" />
            
            <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
            <RegistryValue Root="HKCU" 
                          Key="Software\YourOrganization\GrafanaAlloyWrapper" 
                          Name="installed" 
                          Type="integer" 
                          Value="1" 
                          KeyPath="yes" />
          </Component>
        </Directory>
      </Directory>
    </Directory>
    
    <!-- Features -->
    <Feature Id="ProductFeature" Title="Grafana Alloy Wrapper" Level="1">
      <ComponentRef Id="EmbeddedFiles" />
      <ComponentRef Id="RegistryEntries" />
      <ComponentRef Id="ApplicationShortcut" />
    </Feature>
    
    <!-- Custom Actions -->
    <CustomAction Id="ValidateFiles"
                  Property="POWERSHELLPATH"
                  ExeCommand="-ExecutionPolicy Bypass -File &quot;[POWERSHELLPATH]&quot; -InstallerPath &quot;[INSTALLERPATH]&quot; -ConfigPath &quot;[CONFIGPATH]&quot; -ValidateOnly"
                  Execute="deferred"
                  Impersonate="no"
                  Return="check" />
    
    <CustomAction Id="DeployAlloy"
                  Property="POWERSHELLPATH"
                  ExeCommand="-ExecutionPolicy Bypass -File &quot;[POWERSHELLPATH]&quot; -InstallerPath &quot;[INSTALLERPATH]&quot; -ConfigPath &quot;[CONFIGPATH]&quot;"
                  Execute="deferred"
                  Impersonate="no"
                  Return="check" />
    
    <!-- Install Sequence -->
    <InstallExecuteSequence>
      <Custom Action="ValidateFiles" After="InstallFiles">NOT Installed</Custom>
      <!-- Uncomment the next line to auto-deploy during installation -->
      <!-- <Custom Action="DeployAlloy" After="ValidateFiles">NOT Installed</Custom> -->
    </InstallExecuteSequence>
    
    <!-- UI Configuration -->
    <UIRef Id="WixUI_Minimal" />
    <WixVariable Id="WixUILicenseRtf" Value="License.rtf" />
    
  </Product>
</Wix>
